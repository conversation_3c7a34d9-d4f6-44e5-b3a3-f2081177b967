package com.imile.attendance.infrastructure.repository.warehouse.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.warehouse.dto.WarehouseDetailAbnormalDTO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailAbnormalDO;
import com.imile.attendance.infrastructure.repository.warehouse.param.WarehouseDetailAbnormalParam;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 仓内统计关联异常表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Mapper
@Repository
public interface WarehouseDetailAbnormalMapper extends BaseMapper<WarehouseDetailAbnormalDO> {

    List<WarehouseDetailAbnormalDTO> selectJoinWarehouseDetailList(WarehouseDetailAbnormalParam param);
}
