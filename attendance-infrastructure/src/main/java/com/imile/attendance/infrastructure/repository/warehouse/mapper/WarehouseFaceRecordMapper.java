package com.imile.attendance.infrastructure.repository.warehouse.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseFaceRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;

/**
 * <AUTHOR> 4.0 sonnet
 * @Date 2025-07-09
 * @Description 仓内人脸识别记录表Mapper接口
 */
@Mapper
@Repository
public interface WarehouseFaceRecordMapper extends BaseMapper<WarehouseFaceRecordDO> {

    void updateStatusByUserId(@Param("userId") Long userId, @Param("warehouseDate") Date warehouseDate, @Param("faceRecordStatus") Integer faceRecordStatus);
}
