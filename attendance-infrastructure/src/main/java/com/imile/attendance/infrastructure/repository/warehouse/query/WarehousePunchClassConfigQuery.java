package com.imile.attendance.infrastructure.repository.warehouse.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WarehousePunchClassConfigQuery {
    /**
     * 国家：必填
     */
    private String country;
    /**
     * 部门id列表：必填
     */
    private List<Long> deptIdList;
    /**
     * 班次性质
     */
    private String classNature;
}
