package com.imile.attendance.infrastructure.repository.warehouse.param;

import com.imile.attendance.query.ResourceQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/30
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WarehouseAttendanceConfigParam extends ResourceQuery {

    private String country;

    private String attendanceConfigName;

    private List<Long> deptIds;
}
