package com.imile.attendance.infrastructure.repository.warehouse.param;

import lombok.Data;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @project hrms
 * @description 仓内记录查询条件
 * @date 2024/7/2 21:02:34
 */
@Data
public class WarehouseRecordParam {
    /**
     * 网点id
     */
    private Long ocId;

    /**
     * ocidList
     */
    private List<Long> authOcIdList;

    /**
     * 供应商id
     */
    private List<Long> vendorIdList;

    /**
     * 供应商编码
     */
    private List<String> vendorCodeList;

    /**
     * 供应商ID
     */
    private Long vendorId;

    /**
     * 供应商编码
     */
    private String vendorCode;

    /**
     * 待办日期（开始时间）
     */
    private Date startTime;

    /**
     * 待办日期（结束时间）
     */
    private Date endTime;

    /**
     * 仓内日期
     */
    private Date warehouseDate;

    /**
     * 仓内日期集合
     */
    private List<String> warehouseDateList;

    /**
     * 仓内状态（0:已入仓 1:已离仓 2:提前离仓）
     */
    private Integer warehouseStatus;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户列表
     */
    private List<Long> userIdList;

    /**
     * 用户编码
     */
    private List<String> userCodeList;
    /**
     * 主键id
     */
    private Long id;

    /**
     * 记录类型（1:入仓 2离仓）
     */
    private Integer recordType;

    /**
     * 考勤结果ID
     */
    private Long warehouseDetailId;

    /**
     * 考勤结果idList
     */
    private Collection<Long> warehouseDetailIds;

    /**
     * 考勤状态
     */
    private List<Integer> attendanceStatusList;

    /**
     * 国家
     */
    private String country;

    /**
     * 城市
     */
    private String city;

    /**
     * 班次
     */
    private Long classId;

    /**
     * 用工类型
     */
    private List<String> employeeTypeList;

    /**
     * 异常类型
     */
    private String abnormalType;


}
