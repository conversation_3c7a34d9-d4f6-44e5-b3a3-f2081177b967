package com.imile.attendance.infrastructure.repository.warehouse.query;

import com.imile.common.query.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @project hrms
 * @description 网点员工管理搜索
 * @date 2024/6/29 20:45:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OcUserQuery extends BaseQuery {

    private String country;

    private List<String> cityList;

    private List<Long> ocIdList;

    private List<Long> vendorIdList;

    private List<String> vendorCodeList;

    /**
     * 搜索人员/工号关键词
     */
    private String searchUserKey;

    /**
     * 证件号
     */
    private String certificatesCode;

    /**
     * 用工类型
     */
    private List<String> employeeTypeList;

    /**
     * 用工形式
     */
    private String employmentForm;
}
