package com.imile.attendance.infrastructure.repository.warehouse.param;

import lombok.Data;

import java.util.Date;


/**
 *
 * <AUTHOR>
 * @since 2024/12/06
 */
@Data
public class WarehouseVendorClassesConfirmParam {
    /**
     * 网点id
     */
    private Long ocId;

    /**
     * 供应商id
     */
    private String vendorCode;

    /**
     * 作业时间
     */
    private Date warehouseDate;

    /**
     * 班次ID
     */
    private Long classesId;
}
