package com.imile.attendance.infrastructure.repository.warehouse.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 人员扩展属性表查询
 *
 * <AUTHOR>
 * @since 2025/03/24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserExtendAttrQuery {

    /**
     * 属性key
     */
    private String attrKey;

    /**
     * 属性value
     */
    private String attrValue;

    /**
     * 国家三字码
     */
    private String country;

    /**
     * 员工状态
     */
    private String status;

    /**
     * 员工工资状态
     */
    private String workStatus;

}
