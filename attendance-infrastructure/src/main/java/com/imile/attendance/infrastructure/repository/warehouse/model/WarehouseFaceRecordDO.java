package com.imile.attendance.infrastructure.repository.warehouse.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> 4.0 sonnet
 * @Date 2025-07-09
 * @Description 仓内人脸识别记录表
 */
@ApiModel(description = "仓内人脸识别记录表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("warehouse_face_record")
public class WarehouseFaceRecordDO extends BaseDO {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 人员id
     */
    @ApiModelProperty(value = "人员id")
    private Long userId;

    /**
     * 人员编码
     */
    @ApiModelProperty(value = "人员编码")
    private String userCode;

    /**
     * 仓内日期
     */
    @ApiModelProperty(value = "仓内日期")
    private Date warehouseDate;

    /**
     * 刷脸记录时间
     */
    @ApiModelProperty(value = "刷脸记录时间")
    private Date faceRecordTime;

    /**
     * 人脸照
     */
    @ApiModelProperty(value = "人脸照")
    private String facePhoto;

    /**
     * 识别照
     */
    @ApiModelProperty(value = "识别照")
    private String recognitionPhoto;

    /**
     * 识别得分
     */
    @ApiModelProperty(value = "识别得分")
    private BigDecimal recognitionScore;

    /**
     * 是否被使用（0:未使用 1:已使用）
     */
    @ApiModelProperty(value = "是否被使用（0:未使用 1:已使用）")
    private Integer faceRecordStatus;

    /**
     * 是否证件上传（0:人脸 1:证件）
     */
    @ApiModelProperty(value = "是否证件上传（0:人脸 1:证件）")
    private Integer isCertificatesUpload;
}
