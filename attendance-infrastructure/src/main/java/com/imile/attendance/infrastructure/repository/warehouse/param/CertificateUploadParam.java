package com.imile.attendance.infrastructure.repository.warehouse.param;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/12/18
 */
@Data
public class CertificateUploadParam implements Serializable {

    @NotNull(message = "file cannot be empty")
    @JSONField(serialize = false)
    private MultipartFile file;

    @NotNull(message = "employeeType cannot be empty")
    private String employeeType;

    @NotNull(message = "country cannot be empty")
    private String country;

}
