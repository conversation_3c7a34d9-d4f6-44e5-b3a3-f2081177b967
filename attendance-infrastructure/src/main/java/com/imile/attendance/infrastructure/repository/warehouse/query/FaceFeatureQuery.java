package com.imile.attendance.infrastructure.repository.warehouse.query;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/14
 */
@Data
public class FaceFeatureQuery {
    /**
     * 国家
     */
    private List<String> countryList;

    /**
     * 用户编码
     */
    private List<String> userCodeList;

    /**
     * 用工类型
     */
    private List<String> employeeTypeList;

    private Long lastId;
}
