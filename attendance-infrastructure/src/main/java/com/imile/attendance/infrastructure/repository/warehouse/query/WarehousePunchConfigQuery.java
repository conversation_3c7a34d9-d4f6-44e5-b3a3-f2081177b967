package com.imile.attendance.infrastructure.repository.warehouse.query;

import lombok.Data;

import java.util.Date;

/**
 * {@code @author:} allen
 * {@code @className:} WarehousePunchConfigQuery
 * {@code @since:} 2024-08-20 20:27
 * {@code @description:}
 */
@Data
public class WarehousePunchConfigQuery {
    /**
     * 国家：必填
     */
    private String country;
    /**
     * 部门id：必填
     */
    private Long deptId;
    /**
     * 考勤组打卡方式：必填
     */
    private String punchCardType;
    /**
     * 打卡规则类型
     */
    private String punchConfigType;
    /**
     * 当前时间：非必填：本地时间（不传该时间，会把考勤组下面班次列出来。传了该时间，会把班次最早最晚时间包含该时间的班次放到其他班次前面）
     */
    private Date now;
    /**
     * 考勤日
     */
    private Long dayId;
}
