package com.imile.attendance.infrastructure.repository.warehouse.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import com.imile.attendance.infrastructure.repository.warehouse.query.WarehouseDetailQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 仓内统计表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024/8/27
 */
@Mapper
@Repository
public interface WarehouseDetailMapper extends BaseMapper<WarehouseDetailDO> {

    List<WarehouseDetailDO> selectPage(WarehouseDetailQuery query);

    List<WarehouseDetailDO> selectPcsPage(WarehouseDetailQuery query);

    List<WarehouseDetailDO> selectJoinAbnormalList(WarehouseDetailQuery query);

    List<WarehouseDetailDO> selectJoinRecordList(WarehouseDetailQuery query);

    List<WarehouseDetailDO> selectClassesByCondition(WarehouseDetailQuery query);

    List<WarehouseDetailDO> selectLatestByUserIds(@Param("userIdList") List<Long> userIdList);
}
