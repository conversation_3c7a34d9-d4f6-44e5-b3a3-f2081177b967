<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.report.mapper.AttendanceDayReportMapper">

    <resultMap id="BaseResultMap"
               type="com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="day_id" jdbcType="BIGINT" property="dayId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="user_code" jdbcType="VARCHAR" property="userCode"/>
        <result column="employee_type" jdbcType="VARCHAR" property="employeeType"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="work_status" jdbcType="VARCHAR" property="workStatus"/>
        <result column="dept_id" jdbcType="BIGINT" property="deptId"/>
        <result column="post_id" jdbcType="BIGINT" property="postId"/>
        <result column="location_country" jdbcType="VARCHAR" property="locationCountry"/>
        <result column="location_province" jdbcType="VARCHAR" property="locationProvince"/>
        <result column="location_city" jdbcType="VARCHAR" property="locationCity"/>
        <result column="class_nature" jdbcType="VARCHAR" property="classNature"/>
        <result column="calendar_id" jdbcType="BIGINT" property="calendarId"/>
        <result column="punch_config_id" jdbcType="BIGINT" property="punchConfigId"/>
        <result column="punch_config_type" jdbcType="VARCHAR" property="punchConfigType"/>
        <result column="day_shift_rule" jdbcType="VARCHAR" property="dayShiftRule"/>
        <result column="punch_class_config_id" jdbcType="BIGINT" property="punchClassConfigId"/>
        <result column="reissue_card_config_id" jdbcType="BIGINT" property="reissueCardConfigId"/>
        <result column="over_time_config_id" jdbcType="BIGINT" property="overTimeConfigId"/>
        <result column="leave_minutes" jdbcType="BIGINT" property="leaveMinutes"/>
        <result column="ooo_minutes" jdbcType="BIGINT" property="oooMinutes"/>
        <result column="over_time_minutes" jdbcType="BIGINT" property="overTimeMinutes"/>
        <result column="delay_minutes" jdbcType="BIGINT" property="delayMinutes"/>
        <result column="late_minutes" jdbcType="BIGINT" property="lateMinutes"/>
        <result column="leave_early_minutes" jdbcType="BIGINT" property="leaveEarlyMinutes"/>
        <result column="init_result" jdbcType="TINYINT" property="initResult"/>
        <result column="final_result" jdbcType="TINYINT" property="finalResult"/>
        <result column="actual_attendance_minutes" jdbcType="DECIMAL" property="actualAttendanceMinutes"/>
        <result column="abnormal_work_minutes" jdbcType="DECIMAL" property="abnormalWorkMinutes"/>
        <result column="final_work_minutes" jdbcType="DECIMAL" property="finalWorkMinutes"/>
        <result column="attendance_result" jdbcType="VARCHAR" property="attendanceResult"/>
        <result column="absent_result" jdbcType="TINYINT" property="absentResult"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="record_version" jdbcType="BIGINT" property="recordVersion"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate"/>
        <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode"/>
        <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
        day_id,
        user_id,
        user_name,
        user_code,
        employee_type,
        status,
        work_status,
        dept_id,
        post_id,
        location_country,
        location_province,
        location_city,
        class_nature,
        calendar_id,
        punch_config_id,
        punch_config_type,
        day_shift_rule,
        punch_class_config_id,
        reissue_card_config_id,
        over_time_config_id,
        leave_minutes,
        ooo_minutes,
        over_time_minutes,
        init_result,
        final_result,
        actual_attendance_minutes,
        abnormal_work_minutes,
        final_work_minutes,
        attendance_result,
        absent_result,
        is_delete,
        record_version,
        create_date,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name
    </sql>


    <select id="page" resultType="com.imile.attendance.infrastructure.repository.report.dto.UserMonthReportBaseDTO"
         parameterType="com.imile.attendance.infrastructure.repository.report.query.MonthReportListQuery">
        select
        ui.id as userId,
        ui.user_name as userName,
        ui.user_code as userCode,
        ui.work_status as workStatus,
        ui.status as accountStatus,
        ui.employee_type as employeeType,
        ui.dept_id as deptId,
        ui.post_id as postId,
        ui.location_country as locationCountry,
        ui.location_province as locationProvince,
        ui.location_city as locationCity
        from
        user_info ui
        where ui.is_delete = 0 and ui.is_driver = 0 and user_code is not null
        <if test="workStatus != null and workStatus != ''">
            and ui.work_status = #{workStatus}
        </if>
        <if test="accountStatus != null and accountStatus != ''">
            and ui.status = #{accountStatus}
        </if>
        <if test="employeeTypeList != null and employeeTypeList.size() > 0">
            <foreach collection="employeeTypeList" separator="," open="and ui.employee_type in (" close=")" item="employeeType">
                #{employeeType}
            </foreach>
        </if>
        <if test="deptIds != null and deptIds.size() > 0">
            <foreach collection="deptIds" separator="," open="and ui.dept_id in (" close=")" item="deptId">
                #{deptId}
            </foreach>
        </if>
        <if test="postIds != null and postIds.size() > 0">
            <foreach collection="postIds" separator="," open="and ui.post_id in (" close=")" item="postId">
                #{postId}
            </foreach>
        </if>
        <if test="locationCountry != null and locationCountry != ''">
            and ui.location_country = #{locationCountry}
        </if>
        <if test="locationProvince != null and locationProvince != ''">
            and ui.location_province = #{locationProvince}
        </if>
        <if test="locationCity != null and locationCity != ''">
            and ui.location_city = #{locationCity}
        </if>
        <if test="userIds != null and userIds.size() > 0">
            <foreach collection="userIds" separator="," open="and ui.id in (" close=")" item="userId">
                #{userId}
            </foreach>
        </if>
        <if test="userCodes != null and userCodes.size() > 0">
            <foreach collection="userCodes" separator="," open="and ui.user_code in (" close=")" item="userCode">
                #{userCode}
            </foreach>
        </if>
        <if test="wareHouseCountry != null and wareHouseCountry.size() > 0">
            and not ( ui.location_country in
            <foreach collection="wareHouseCountry" separator="," open="(" close=")" item="country">
                #{country}
            </foreach>
            and employee_type = 'OSFixedsalary'
            and is_warehouse_staff = 1
            )
        </if>
        <if test="osCountry != null and osCountry.size() > 0">
            and not ( ui.location_country not in
            <foreach collection="osCountry" separator="," open="(" close=")" item="country">
                #{country}
            </foreach>
            and employee_type = 'OSFixedsalary'
            )
        </if>
        <include refid="permissionAndDeptConditions"/>
    </select>



    <select id="pageCycle" resultType="com.imile.attendance.infrastructure.repository.report.dto.UserMonthReportBaseDTO"
            parameterType="com.imile.attendance.infrastructure.repository.report.query.MonthReportListQuery">

        <trim prefixOverrides="union">
            <if test="workStatus == null or workStatus == '' or 'ON_JOB'.equals(workStatus) ">
                select
                ui.id as userId,
                ui.user_name as userName,
                ui.user_code as userCode,
                ui.work_status as workStatus,
                ui.status as accountStatus,
                ui.employee_type as employeeType,
                ui.dept_id as deptId,
                ui.post_id as postId,
                ui.location_country as locationCountry,
                ui.location_province as locationProvince,
                ui.location_city as locationCity
                from
                user_info ui
                left join user_entry_record uer on uer.user_id = ui.id and uer.is_delete = 0 and uer.entry_status = 'ENTRY'
                left join attendance_cycle_calendar cy ON cy.country = ui.location_country AND cy.attendance_year = #{attendanceYear} AND cy.attendance_month = #{attendanceMonth}
                where ui.is_delete = 0 and ui.is_driver = 0 and user_code is not null
                and ui.work_status = 'ON_JOB'
                and (uer.confirm_date is null or uer.confirm_date &lt;= cy.cycle_end_date)
                <if test="accountStatus != null and accountStatus != ''">
                    and ui.status = #{accountStatus}
                </if>
                <if test="employeeTypeList != null and employeeTypeList.size() > 0">
                    <foreach collection="employeeTypeList" separator="," open="and ui.employee_type in (" close=")" item="employeeType">
                        #{employeeType}
                    </foreach>
                </if>
                <if test="deptIds != null and deptIds.size() > 0">
                    <foreach collection="deptIds" separator="," open="and ui.dept_id in (" close=")" item="deptId">
                        #{deptId}
                    </foreach>
                </if>
                <if test="postIds != null and postIds.size() > 0">
                    <foreach collection="postIds" separator="," open="and ui.post_id in (" close=")" item="postId">
                        #{postId}
                    </foreach>
                </if>
                <if test="locationCountry != null and locationCountry != ''">
                    and ui.location_country = #{locationCountry}
                </if>
                <if test="locationProvince != null and locationProvince != ''">
                    and ui.location_province = #{locationProvince}
                </if>
                <if test="locationCity != null and locationCity != ''">
                    and ui.location_city = #{locationCity}
                </if>
                <if test="userIds != null and userIds.size() > 0">
                    <foreach collection="userIds" separator="," open="and ui.id in (" close=")" item="userId">
                        #{userId}
                    </foreach>
                </if>
                <if test="userCodes != null and userCodes.size() > 0">
                    <foreach collection="userCodes" separator="," open="and ui.user_code in (" close=")" item="userCode">
                        #{userCode}
                    </foreach>
                </if>
                <if test="wareHouseCountry != null and wareHouseCountry.size() > 0">
                    and not ( ui.location_country in
                    <foreach collection="wareHouseCountry" separator="," open="(" close=")" item="country">
                        #{country}
                    </foreach>
                    and employee_type = 'OSFixedsalary'
                    and is_warehouse_staff = 1
                    )
                </if>
                <if test="osCountry != null and osCountry.size() > 0">
                    and not ( ui.location_country not in
                    <foreach collection="osCountry" separator="," open="(" close=")" item="country">
                        #{country}
                    </foreach>
                    and employee_type = 'OSFixedsalary'
                    )
                </if>
                <include refid="permissionAndDeptConditions"/>
            </if>
            <if test="workStatus == null or workStatus == '' or 'DIMISSION'.equals(workStatus) ">
                union
                select
                ui.id as userId,
                ui.user_name as userName,
                ui.user_code as userCode,
                ui.work_status as workStatus,
                ui.status as accountStatus,
                ui.employee_type as employeeType,
                ui.dept_id as deptId,
                ui.post_id as postId,
                ui.location_country as locationCountry,
                ui.location_province as locationProvince,
                ui.location_city as locationCity
                from
                user_info ui
                left join user_dimission_record udr on udr.user_id = ui.id and udr.is_delete = 0 and udr.dimission_status = 'DIMISSION'
                left join attendance_cycle_calendar cy ON cy.country = ui.location_country AND cy.attendance_year = #{attendanceYear} AND cy.attendance_month = #{attendanceMonth}
                where ui.is_delete = 0 and ui.is_driver = 0 and user_code is not null
                and udr.actual_dimission_date BETWEEN cy.cycle_start_date AND cy.cycle_end_date
                <if test="accountStatus != null and accountStatus != ''">
                    and ui.status = #{accountStatus}
                </if>
                <if test="employeeTypeList != null and employeeTypeList.size() > 0">
                    <foreach collection="employeeTypeList" separator="," open="and ui.employee_type in (" close=")" item="employeeType">
                        #{employeeType}
                    </foreach>
                </if>
                <if test="deptIds != null and deptIds.size() > 0">
                    <foreach collection="deptIds" separator="," open="and ui.dept_id in (" close=")" item="deptId">
                        #{deptId}
                    </foreach>
                </if>
                <if test="postIds != null and postIds.size() > 0">
                    <foreach collection="postIds" separator="," open="and ui.post_id in (" close=")" item="postId">
                        #{postId}
                    </foreach>
                </if>
                <if test="locationCountry != null and locationCountry != ''">
                    and ui.location_country = #{locationCountry}
                </if>
                <if test="locationProvince != null and locationProvince != ''">
                    and ui.location_province = #{locationProvince}
                </if>
                <if test="locationCity != null and locationCity != ''">
                    and ui.location_city = #{locationCity}
                </if>
                <if test="userIds != null and userIds.size() > 0">
                    <foreach collection="userIds" separator="," open="and ui.id in (" close=")" item="userId">
                        #{userId}
                    </foreach>
                </if>
                <if test="userCodes != null and userCodes.size() > 0">
                    <foreach collection="userCodes" separator="," open="and ui.user_code in (" close=")" item="userCode">
                        #{userCode}
                    </foreach>
                </if>
                <if test="wareHouseCountry != null and wareHouseCountry.size() > 0">
                    and not ( ui.location_country in
                    <foreach collection="wareHouseCountry" separator="," open="(" close=")" item="country">
                        #{country}
                    </foreach>
                    and employee_type = 'OSFixedsalary'
                    and is_warehouse_staff = 1
                    )
                </if>
                <if test="osCountry != null and osCountry.size() > 0">
                    and not ( ui.location_country not in
                    <foreach collection="osCountry" separator="," open="(" close=")" item="country">
                        #{country}
                    </foreach>
                    and employee_type = 'OSFixedsalary'
                    )
                </if>
                <include refid="permissionAndDeptConditions"/>
            </if>
        </trim>
    </select>




    <!-- 权限和部门条件片段 -->
    <sql id="permissionAndDeptConditions">
        <!-- 同时具有部门和国家权限 -->
        <if test="authDeptIdList != null and authDeptIdList.size() > 0 and
            authLocationCountryList != null and authLocationCountryList.size() > 0">
            and (ui.dept_id in
            <foreach collection="authDeptIdList" item="deptId" separator="," open="(" close=")">
                #{deptId}
            </foreach>
            or ui.location_country in
            <foreach collection="authLocationCountryList" item="country" separator="," open="(" close=")">
                #{country}
            </foreach>
            )
        </if>
        <!-- 只有部门权限 -->
        <if test="authDeptIdList != null and authDeptIdList.size() > 0 and
            (authLocationCountryList == null or authLocationCountryList.size() == 0)">
            and (ui.dept_id in
            <foreach collection="authDeptIdList" item="deptId" separator="," open="(" close=")">
                #{deptId}
            </foreach>
            )
        </if>
        <!-- 只有国家权限 -->
        <if test="authLocationCountryList != null and authLocationCountryList.size() > 0 and
            (authDeptIdList == null or authDeptIdList.size() == 0)">
            and (ui.location_country in
            <foreach collection="authLocationCountryList" item="country" separator="," open="(" close=")">
                #{country}
            </foreach>
            )
        </if>
        <!-- 筛选条件 -->
        <if test="locationCountry != null and locationCountry != ''">
            and ui.location_country = #{locationCountry}
        </if>
        <if test="deptIds != null and deptIds.size() > 0">
            and ui.dept_id in
            <foreach collection="deptIds" item="deptId" separator="," open="(" close=")">
                #{deptId}
            </foreach>
        </if>
    </sql>
</mapper>
