<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.warehouse.mapper.WarehouseDetailMapper">
    <select id="selectJoinRecordList"
            parameterType="com.imile.attendance.infrastructure.repository.warehouse.query.WarehouseDetailQuery"
            resultType="com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO">
        SELECT
        distinct(wd.id)
        FROM warehouse_detail wd
        inner join warehouse_record wr on wd.id = wr.warehouse_detail_id
        <where>
            wd.warehouse_date = #{warehouseDate}
            <if test="ocId!=null and ocId!=''">
                AND wd.oc_id = #{ocId}
            </if>

            <if test="vendorCode!=null and vendorCode!=''">
                AND wd.vendor_code = #{vendorCode}
            </if>

            <if test="classesId!=null and classesId!=''">
                AND wd.classes_id = #{classesId}
            </if>

            <if test="employeeTypeList!=null and employeeTypeList.size()>0">
                <foreach collection="employeeTypeList" item="employeeType" open="and wd.employee_type in (" close=")"
                         separator=",">
                    #{employeeType}
                </foreach>
            </if>

            <if test="recordType!=null and recordType!=''">
                AND wr.record_type = #{recordType}
                AND wr.is_delete = 0
            </if>

            AND wd.is_delete = 0
        </where>
    </select>

    <select id="selectJoinAbnormalList"
            parameterType="com.imile.attendance.infrastructure.repository.warehouse.query.WarehouseDetailQuery"
            resultType="com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO">
        SELECT
        distinct(wd.id)
        FROM warehouse_detail wd
        inner join warehouse_detail_abnormal wda on wd.id = wda.warehouse_detail_id
        <where>
            wd.is_delete = 0
            <if test="warehouseDate!=null">
                AND wd.warehouse_date = #{warehouseDate}
            </if>
            <if test="startDate!=null">
                AND wd.warehouse_date >= #{startDate}
            </if>
            <if test="endDate!=null">
                AND wd.warehouse_date &lt; #{endDate}
            </if>
            <if test="ocId!=null and ocId!=''">
                AND wd.oc_id = #{ocId}
            </if>

            <if test="vendorCode!=null and vendorCode!=''">
                AND wd.vendor_code = #{vendorCode}
            </if>

            <if test="classesId!=null and classesId!=''">
                AND wd.classes_id = #{classesId}
            </if>

            <if test="employeeTypeList!=null and employeeTypeList.size()>0">
                <foreach collection="employeeTypeList" item="employeeType" open="and wd.employee_type in (" close=")"
                         separator=",">
                    #{employeeType}
                </foreach>
            </if>

            <if test="abnormalType!=null and abnormalType!=''">
                AND wda.abnormal_type = #{abnormalType}
                AND wd.attendance_status = 2 and wda.is_delete=0
            </if>
        </where>
    </select>

    <select id="selectClassesByCondition"
            resultType="com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO">
        SELECT
        distinct(classes_id),classes_type,classes_name
        FROM warehouse_detail
        <where>
            warehouse_date = #{warehouseDate}
            AND oc_id = #{ocId}
            AND classes_name is not null
            <if test="vendorCode!=null and vendorCode!=''">
                AND vendor_code = #{vendorCode}
            </if>
            AND is_delete = 0
        </where>
    </select>

    <select id="selectPage"
            resultType="com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO">
        SELECT
        id,country,city,oc_id,vendor_id,vendor_code,user_id,user_code,warehouse_status,warehouse_date,in_time,in_face_record_id,out_time,out_face_record_id,stay_duration,
        user_oc_id,user_vendor_id,user_vendor_code,salary_date,classes_type,classes_name,attendance_status,required_attendance_time,legal_working_hours,actual_attendance_time,absence_time,
        overtime_hours,attendance_type,leave_type,leave_hours,warehouse_actual_attendance_time,oc_longitude,oc_latitude,warehouse_attendance_code,employee_type,confirm_status,punch_status,
        actual_working_hours,employment_form
        FROM warehouse_detail
        <where>
            warehouse_date BETWEEN #{startDate} AND #{endDate}
            <if test="country != null and country!=''">
                AND country = #{country}
            </if>
            <if test="cityList != null and cityList.size() > 0">
                AND city IN
                <foreach item="city" collection="cityList" open="(" close=")" separator=",">
                    #{city}
                </foreach>
            </if>
            <if test="ocIdList != null and ocIdList.size() > 0">
                AND oc_id IN
                <foreach item="ocId" collection="ocIdList" open="(" close=")" separator=",">
                    #{ocId}
                </foreach>
            </if>
            <if test="vendorIdList != null and vendorIdList.size() > 0">
                AND vendor_id IN
                <foreach item="vendorId" collection="vendorIdList" open="(" close=")" separator=",">
                    #{vendorId}
                </foreach>
            </if>
            <if test="employeeTypeList != null and employeeTypeList.size() > 0">
                AND employee_type IN
                <foreach item="employeeType" collection="employeeTypeList" open="(" close=")" separator=",">
                    #{employeeType}
                </foreach>
            </if>
            <if test="userIdList != null and userIdList.size() > 0">
                AND user_id IN
                <foreach item="userId" collection="userIdList" open="(" close=")" separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="result != null and result !=''">
                AND attendance_status = #{result}
            </if>
            <if test="warehouseAttendanceCode != null and warehouseAttendanceCode !=''">
                AND warehouse_attendance_code = #{warehouseAttendanceCode}
            </if>
            <if test="classesId != null and classesId !=''">
                AND classes_id = #{classesId}
            </if>
            <if test="punchStatus != null and punchStatus !=''">
                AND punch_status = #{punchStatus}
            </if>
            <if test="confirmStatusList != null and confirmStatusList.size() > 0">
                AND confirm_status IN
                <foreach item="confirmStatus" collection="confirmStatusList" open="(" close=")" separator=",">
                    #{confirmStatus}
                </foreach>
            </if>
            <if test="employmentForm != null and employmentForm!=''">
                AND employment_form = #{employmentForm}
            </if>
            and is_delete = 0
        </where>
        <if test="reportType == 'MONTH'">
            GROUP BY user_id,oc_id,vendor_id
        </if>
        <if test="reportType == 'DAY'">
            order by id desc
        </if>
    </select>

    <select id="selectPcsPage"
            resultType="com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO">
        SELECT
        id,country,city,oc_id,user_oc_id,user_id,user_code,vendor_code,warehouse_date,pcs_status,vendor_id,attendance_status,classes_name,classes_type,actual_attendance_time
        FROM warehouse_detail
        <where>
            <if test="startDate != null and endDate != null">
                warehouse_date BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="country != null and country!=''">
                AND country = #{country}
            </if>
            <if test="city != null and city!=''">
                AND city = #{city}
            </if>
            <if test="ocIdList != null and ocIdList.size() > 0">
                AND oc_id IN
                <foreach item="ocId" collection="ocIdList" open="(" close=")" separator=",">
                    #{ocId}
                </foreach>
            </if>
            <if test="vendorCodeList != null and vendorCodeList.size() > 0">
                AND vendor_code IN
                <foreach item="vendorCode" collection="vendorCodeList" open="(" close=")" separator=",">
                    #{vendorCode}
                </foreach>
            </if>

            <if test="userIdList != null and userIdList.size() > 0">
                AND user_id IN
                <foreach item="userId" collection="userIdList" open="(" close=")" separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="pcsStatus != null and pcsStatus !=''">
                AND pcs_status = #{pcsStatus}
            </if>
            <if test="attendanceStatus != null and attendanceStatus !=''">
                AND attendance_status = #{attendanceStatus}
            </if>
            <if test="classesTypeList != null and classesTypeList.size() > 0">
                AND classes_type IN
                <foreach item="classesType" collection="classesTypeList" open="(" close=")" separator=",">
                    #{classesType}
                </foreach>
            </if>
            AND employee_type = 'OSFixedsalary'
            AND is_delete = 0
        </where>
        <if test="reportType == 'WEEK' || reportType == 'MONTH'">
            GROUP BY user_id,oc_id,vendor_id
            order by warehouse_date asc
        </if>
    </select>

    <select id="selectLatestByUserIds"
            resultType="com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO">
        SELECT t1.*
        FROM warehouse_detail t1
            INNER JOIN (
            SELECT
                user_id,
                MAX(warehouse_date) AS max_date
            FROM warehouse_detail
            where employee_type = 'OSFixedsalary' and is_delete = 0
            GROUP BY user_id
        ) t2 ON t1.user_id = t2.user_id AND t1.warehouse_date = t2.max_date
        <where>
            t1.is_delete = 0
            <if test="userIdList != null and userIdList.size() > 0">
                AND t2.user_id IN
                <foreach item="userId" collection="userIdList" open="(" close=")" separator=",">
                    #{userId}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
