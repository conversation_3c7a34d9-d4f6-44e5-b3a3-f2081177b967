<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.warehouse.mapper.WarehouseBlackListMapper">
  <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseBlackListDO">
    <!--@mbg.generated-->
    <!--@Table warehouse_black_list-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="is_delete" jdbcType="BOOLEAN" property="isDelete" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate" />
    <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode" />
    <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName" />
    <result column="record_version" jdbcType="INTEGER" property="recordVersion" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_code" jdbcType="VARCHAR" property="userCode" />
    <result column="oc_id" jdbcType="BIGINT" property="ocId" />
    <result column="vendor_code" jdbcType="VARCHAR" property="vendorCode" />
    <result column="classes_id" jdbcType="BIGINT" property="classesId" />
    <result column="warehouse_date" jdbcType="DATE" property="warehouseDate" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="employee_type" jdbcType="VARCHAR" property="employeeType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, is_delete, create_date, create_user_code, create_user_name, last_upd_date, last_upd_user_code, 
    last_upd_user_name, record_version, user_id, user_code, oc_id, vendor_code, classes_id,
    warehouse_date, `type`, reason, employee_type
  </sql>
</mapper>