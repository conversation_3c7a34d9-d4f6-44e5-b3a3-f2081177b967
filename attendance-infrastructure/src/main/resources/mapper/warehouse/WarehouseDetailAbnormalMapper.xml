<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.warehouse.mapper.WarehouseDetailAbnormalMapper">

    <select id="selectJoinWarehouseDetailList"
            parameterType="com.imile.attendance.infrastructure.repository.warehouse.param.WarehouseDetailAbnormalParam"
            resultType="com.imile.attendance.infrastructure.repository.warehouse.dto.WarehouseDetailAbnormalDTO">
        SELECT
        wda.id,wda.abnormal_id,wda.abnormal_type,wd.id,wd.oc_id,wd.vendor_id,wd.employment_form,wda.warehouse_detail_id,wd.actual_attendance_time,wd.actual_working_hours
        FROM warehouse_detail_abnormal wda
        inner join warehouse_detail wd  on wda.warehouse_detail_id = wd.id
        <where>
            wda.is_delete = 0 and wd.is_delete = 0
            <if test="abnormalIdList!=null and abnormalIdList.size()>0">
                <foreach collection="abnormalIdList" item="abnormalId" open="and wda.abnormal_id in (" close=")"
                         separator=",">
                    #{abnormalId}
                </foreach>
            </if>
            <if test="startDate!=null">
                AND wd.warehouse_date >= #{startDate}
            </if>
            <if test="endDate!=null">
                AND wd.warehouse_date &lt;= #{endDate}
            </if>
            <if test="country!=null and country!=''">
                AND wd.country = #{country}
            </if>
            <if test="employmentForm!=null and employmentForm!=''">
                AND wd.employment_form = #{employmentForm}
            </if>
            <if test="ocIdList!=null and ocIdList.size()>0">
                <foreach collection="ocIdList" item="ocId" open="and wd.oc_id in (" close=")"
                         separator=",">
                    #{ocId}
                </foreach>
            </if>
            <if test="vendorIdList!=null and vendorIdList.size()>0">
                <foreach collection="vendorIdList" item="vendorId" open="and wd.vendor_id in (" close=")"
                         separator=",">
                    #{vendorId}
                </foreach>
            </if>
        </where>
    </select>

</mapper>
