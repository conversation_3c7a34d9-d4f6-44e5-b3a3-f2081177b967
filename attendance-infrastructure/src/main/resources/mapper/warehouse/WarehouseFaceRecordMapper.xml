<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.warehouse.mapper.WarehouseFaceRecordMapper">
    <update id="updateStatusByUserId">
        UPDATE warehouse_face_record
        set face_record_status = #{faceRecordStatus}
        WHERE user_id = #{userId}
          and warehouse_date = #{warehouseDate}
    </update>
</mapper>
