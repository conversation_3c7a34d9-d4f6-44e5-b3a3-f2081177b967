package com.imile.attendance.warehouse.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 仓内考勤PCS报告
 *
 * <AUTHOR>
 * @since 2024/9/27
 */
@Data
public class WarehousePcsReportDTO implements Serializable {

    private Long id;

    /**
     * 国家
     */
    private String country;

    /**
     * 城市
     */
    private String city;

    /**
     * 操作网点id
     */
    private Long inOcId;

    /**
     * 操作网点名称
     */
    private String inOcName;

    /**
     * 供应商编码
     */
    private String inVendorCode;

    /**
     * 用户所属网点
     */
    private Long ocId;

    /**
     * 用户所属网点名称
     */
    private String ocName;

    /**
     * 员工账号
     */
    private String userCode;

    /**
     * 员工名称
     */
    private String userName;

    /**
     * 英文名
     */
    private String userNameEn;

    /**
     * 考勤日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date warehouseDate;

    /**
     * 上班打卡
     */
    private Boolean clockIn;

    /**
     * 下班打卡
     */
    private Boolean clockOut;

    /**
     * 操作状态
     */
    private Integer pcsStatus;

    /**
     * 操作天数
     */
    private Integer days;

    /**
     * 操作状态正常天数
     */
    private Integer normalDays;

    /**
     * 操作状态异常天数
     */
    private Integer abnormalDays;

    /**
     * 班次名称
     */
    private String classesName;

    /**
     * 班次类型
     */
    private Integer classesType;

    /**
     * 考勤状态
     */
    private Integer attendanceStatus;

    /**
     * 实际出勤时长
     */
    private BigDecimal actualAttendanceTime;

    /**
     * 证件号
     */
    private String certificateCode;
}
