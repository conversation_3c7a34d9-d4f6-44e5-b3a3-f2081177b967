package com.imile.attendance.warehouse.dto;

import lombok.Data;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.function.BiConsumer;


/**
 * 仓内考勤PCS报告数量
 *
 * <AUTHOR>
 * @since 2024/9/30
 */
@Data
public class WarehousePcsMonthReportPassRateDTO implements Serializable {

    private BigDecimal date1;

    private BigDecimal date2;
    private BigDecimal date3;
    private BigDecimal date4;
    private BigDecimal date5;
    private BigDecimal date6;
    private BigDecimal date7;
    private BigDecimal date8;
    private BigDecimal date9;
    private BigDecimal date10;
    private BigDecimal date11;
    private BigDecimal date12;
    private BigDecimal date13;
    private BigDecimal date14;
    private BigDecimal date15;
    private BigDecimal date16;
    private BigDecimal date17;
    private BigDecimal date18;
    private BigDecimal date19;
    private BigDecimal date20;
    private BigDecimal date21;
    private BigDecimal date22;
    private BigDecimal date23;
    private BigDecimal date24;
    private BigDecimal date25;
    private BigDecimal date26;
    private BigDecimal date27;
    private BigDecimal date28;
    private BigDecimal date29;
    private BigDecimal date30;
    private BigDecimal date31;

    // 使用反射来动态获取 setter 方法
    public BiConsumer<WarehousePcsMonthReportPassRateDTO, BigDecimal> getSetter(Integer i) {
        if (i == null || i < 1 || i > 31) {
            throw new IllegalArgumentException(" index must be between 1 and 31");
        }

        String fieldName = "date" + i + "";
        try {
            // 获取字段
            Field field = this.getClass().getDeclaredField(fieldName);
            // 确保字段可访问
            field.setAccessible(true);
            // 获取 setter 方法名，假设遵循 JavaBean 命名规范
            String setterName = "set" + field.getName().substring(0, 1).toUpperCase() + field.getName().substring(1);
            // 获取 setter 方法
            Method setterMethod = this.getClass().getMethod(setterName, BigDecimal.class);
            // 使用 lambda 表达式和 Method.invoke 来创建 BiConsumer
            return (vo, value) -> {
                try {
                    setterMethod.invoke(vo, value);
                } catch (Exception e) {
                    throw new RuntimeException("Failed to set field " + fieldName, e);
                }
            };
        } catch (NoSuchFieldException | NoSuchMethodException e) {
            throw new IllegalArgumentException("No such field or setter method for  " + i, e);
        }
    }

}
