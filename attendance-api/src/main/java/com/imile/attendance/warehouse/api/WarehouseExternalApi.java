package com.imile.attendance.warehouse.api;

import com.imile.attendance.warehouse.dto.UserEmploymentFormDTO;
import com.imile.attendance.warehouse.dto.WarehousePcsMonthReportCountDTO;
import com.imile.attendance.warehouse.dto.WarehousePcsMonthReportPassRateDTO;
import com.imile.attendance.warehouse.dto.WarehousePcsReportDTO;
import com.imile.attendance.warehouse.param.GetPcsReportByConditionParam;
import com.imile.attendance.warehouse.param.GetPcsReportCountByConditionParam;
import com.imile.attendance.warehouse.param.UserEmploymentFormParam;
import com.imile.common.page.PaginationResult;
import com.imile.rpc.common.RpcResult;

import java.util.List;


/**
 * PCS调用接口
 *
 * <AUTHOR>
 * @since 2024/9/30
 */
public interface WarehouseExternalApi {

    RpcResult<PaginationResult<WarehousePcsReportDTO>> pcsDateReport(GetPcsReportByConditionParam param);

    RpcResult<PaginationResult<WarehousePcsReportDTO>> pcsWeekOrMonthReport(GetPcsReportByConditionParam param);

    RpcResult<WarehousePcsMonthReportCountDTO> pcsDateReportCount(GetPcsReportCountByConditionParam param);

    RpcResult<WarehousePcsMonthReportPassRateDTO> pcsDateMonthReportPassRate(GetPcsReportCountByConditionParam param);

    RpcResult<List<UserEmploymentFormDTO>> getEmploymentFormByUserCodes(UserEmploymentFormParam param);
}
