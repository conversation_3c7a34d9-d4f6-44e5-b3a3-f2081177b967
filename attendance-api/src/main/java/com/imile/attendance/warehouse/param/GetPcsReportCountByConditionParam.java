package com.imile.attendance.warehouse.param;

import lombok.Data;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/9/30
 */
@Data
public class GetPcsReportCountByConditionParam implements Serializable {

    /**
     * 供应商编码
     */
    private String vendorCode;

    /**
     * 网点
     */
    private List<Long> ocIdList;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 城市
     */
    private String city;

    /**
     * 国家
     */
    private String country;

    public void check(){
        Assert.notNull(vendorCode,"vendorCode cannot be empty");
        Assert.notNull(startDate,"startDate cannot be empty");
        Assert.notNull(endDate,"endDate cannot be empty");
        Assert.notEmpty(ocIdList,"ocIdList cannot be empty");
    }
}
